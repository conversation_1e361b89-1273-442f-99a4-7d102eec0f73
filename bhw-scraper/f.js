import { ProxyAgent } from 'undici';

const proxyAgent = new ProxyAgent('http://127.0.0.1:7890');

fetch("https://scira.ai/api/search", {
    dispatcher: proxyAgent,
    "headers": {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9",
        "content-type": "application/json",
        "priority": "u=1, i",
        "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"macOS\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "cookie": "better-auth.session_token=NO53goAlTmJ9L75JatTqNKOIPwHQwPiQ.%2FNoQsq0AwhNkiLSd2W7qYo%2Bnv9qYwbhJ4%2F13HEiDFJc%3D; ph_phc_iy7AsVPrSER6rEqnru1DlRr1rIy0GVYRilMbTIGUYrK_posthog=%7B%22distinct_id%22%3A%220197dd5d-5c5d-76eb-98e5-2ec206fc5a9f%22%2C%22%24sesid%22%3A%5B1753795866997%2C%2201985660-c07f-73f3-95c1-9357484d5b02%22%2C1753795838078%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Fscira.ai%2F%22%7D%7D",
        "Referer": "https://scira.ai/search/866d59f6-3219-487e-ad5c-49698f791709",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    },
    "body": "{\"id\":\"866d59f6-3219-487e-ad5c-49698f791709\",\"messages\":[{\"id\":\"5Mg20vAH9gZ1KdWb\",\"createdAt\":\"2025-07-29T13:31:12.590Z\",\"role\":\"user\",\"content\":\"how to use mastra ai\",\"parts\":[{\"type\":\"text\",\"text\":\"how to use mastra ai\"}]}],\"model\":\"scira-anthropic\",\"group\":\"youtube\",\"timezone\":\"Asia/Shanghai\",\"selectedVisibilityType\":\"private\",\"isCustomInstructionsEnabled\":true}",
    "method": "POST"
}).then(res => res.text()).then(console.log)